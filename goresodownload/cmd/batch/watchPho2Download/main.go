/*
This is watchPho2Download batch.
New config:
        ./start.sh -n watchPho2Download -d "goresodownload" -cmd "cmd/batch/watchPho2Download/main.go force dryrun"
createDate:    2025-08-21
Author:        <PERSON><PERSON> xiaowei
Run frequency: always
*/

package main

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"os/signal"
	"strings"
	"sync"
	"syscall"
	"time"

	gobase "github.com/real-rm/gobase"
	golog "github.com/real-rm/golog"
	gomongo "github.com/real-rm/gomongo"
	"github.com/real-rm/goprocess"
	"github.com/real-rm/goresodownload"
	"github.com/real-rm/gowatch"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

var (
	gWatchedObject     *gowatch.WatchObject
	gUpdateSysData     gowatch.UpdateSysDataFunc
	SysdataCol         *gomongo.MongoCollection
	PhotoToDownloadCol *gomongo.MongoCollection
	QueueCol           *gomongo.MongoCollection
	gProcessMonitor    *goprocess.ProcessMonitor
	downloadQueue      *goresodownload.ResourceDownloadQueue
	dryRun             bool
	lastTokenPrintTs   = time.Now()
	gStat              = make(map[string]interface{})
	tokenMu            sync.Mutex
)

const (
	HALF_DAY_IN_MS                = 12 * 3600 * 1000
	UPDATE_PROCESS_STATE_INTERVAL = 10 * 60 * 1000 // 10 minutes
	PROCESS_STATUS_ID             = "watchPho2Download"
	DEFAULT_PRIORITY              = 60000
)

// PhotoToDownload represents the structure of photo_to_download table
type PhotoToDownload struct {
	ID  string `bson:"_id"` // propId
	Sid string `bson:"sid"`
	Src string `bson:"src"`
}

func init() {
	// Initialize base[config, logging]
	if err := gobase.InitBase(); err != nil {
		golog.Fatalf("Failed to initialize base: %v", err)
	}

	// Initialize MongoDB last (after config is loaded)
	if err := gomongo.InitMongoDB(); err != nil {
		golog.Fatalf("Failed to initialize MongoDB: %v", err)
	}

	// Initialize collections
	SysdataCol = gomongo.Coll("rni", "sysdata")
	PhotoToDownloadCol = gomongo.Coll("vow", "photo_to_download")
	QueueCol = gomongo.Coll("rni", "reso_photo_download_queue")
	gUpdateSysData = gowatch.GetUpdateSysdataFunction(SysdataCol, PROCESS_STATUS_ID)

	// Initialize ProcessMonitor
	var err error
	gProcessMonitor, err = goprocess.NewProcessMonitor(goprocess.ProcessMonitorOptions{
		ProcessStatusCol: gomongo.Coll("rni", "processStatus"),
		UpdateInterval:   time.Duration(UPDATE_PROCESS_STATE_INTERVAL) * time.Millisecond,
		Multiplier:       1.5, // Allow 50% more time than the update interval
		ID:               PROCESS_STATUS_ID,
	})
	if err != nil {
		golog.Fatalf("Failed to initialize ProcessMonitor: %v", err)
	}

	// Initialize ResourceDownloadQueue
	downloadQueue, err = goresodownload.NewResourceDownloadQueue(QueueCol)
	if err != nil {
		golog.Fatalf("Failed to initialize ResourceDownloadQueue: %v", err)
	}
}

// ProcessInitialData processes all existing data in photo_to_download table on startup
func ProcessInitialData() error {
	golog.Info("Processing initial data from photo_to_download table")

	ctx := context.Background()
	cursor, err := PhotoToDownloadCol.Find(ctx, bson.M{})
	if err != nil {
		return fmt.Errorf("failed to find documents in photo_to_download: %w", err)
	}
	defer cursor.Close(ctx)

	processedCount := 0
	errorCount := 0

	for cursor.Next(ctx) {
		var photo PhotoToDownload
		if err := cursor.Decode(&photo); err != nil {
			golog.Error("Failed to decode photo document", "error", err)
			errorCount++
			continue
		}

		if err := processPhotoToDownload(photo); err != nil {
			golog.Error("Failed to process initial photo", "id", photo.ID, "error", err)
			errorCount++
			continue
		}
		processedCount++
	}

	if err := cursor.Err(); err != nil {
		return fmt.Errorf("cursor error: %w", err)
	}

	golog.Info("Initial data processing completed",
		"processed", processedCount,
		"errors", errorCount)
	return nil
}

// processPhotoToDownload adds photo to queue and deletes from photo_to_download
func processPhotoToDownload(photo PhotoToDownload) error {
	if dryRun {
		golog.Info("DryRun: Would process photo", "id", photo.ID, "src", photo.Src)
		return nil
	}

	// Add to queue with priority 60000
	if err := downloadQueue.AddToQueue(photo.ID, DEFAULT_PRIORITY, photo.Src); err != nil {
		return fmt.Errorf("failed to add to queue: %w", err)
	}

	// Delete from photo_to_download table
	ctx := context.Background()
	result, err := PhotoToDownloadCol.DeleteOne(ctx, bson.M{"_id": photo.ID})
	if err != nil {
		return fmt.Errorf("failed to delete from photo_to_download: %w", err)
	}

	if result.DeletedCount == 0 {
		golog.Warn("No document deleted", "id", photo.ID)
	}

	golog.Info("Successfully processed photo", "id", photo.ID, "src", photo.Src)
	return nil
}

// ProcessInsert handles property insertion
func ProcessInsert(changeDoc bson.M, prop bson.M) error {
	if prop["_id"] == nil {
		golog.Error("invalid prop: no id")
		return fmt.Errorf("no id")
	}

	photo := PhotoToDownload{
		ID:  fmt.Sprintf("%v", prop["_id"]),
		Sid: getStringValue(prop, "sid"),
		Src: getStringValue(prop, "src"),
	}

	golog.Info("Processing insert", "propId", photo.ID, "src", photo.Src)
	return processPhotoToDownload(photo)
}

// ProcessReplace handles property replacement
func ProcessReplace(changeDoc bson.M, prop bson.M) error {
	if prop["_id"] == nil {
		golog.Error("invalid prop: no id")
		return fmt.Errorf("no id")
	}

	photo := PhotoToDownload{
		ID:  fmt.Sprintf("%v", prop["_id"]),
		Sid: getStringValue(prop, "sid"),
		Src: getStringValue(prop, "src"),
	}

	golog.Info("Processing replace", "propId", photo.ID, "src", photo.Src)
	return processPhotoToDownload(photo)
}

// ProcessUpdate handles property updates
func ProcessUpdate(changeDoc bson.M, prop bson.M) error {
	if prop["_id"] == nil {
		golog.Error("invalid prop: no id")
		return fmt.Errorf("no id")
	}

	photo := PhotoToDownload{
		ID:  fmt.Sprintf("%v", prop["_id"]),
		Sid: getStringValue(prop, "sid"),
		Src: getStringValue(prop, "src"),
	}

	golog.Info("Processing update", "propId", photo.ID, "src", photo.Src)
	return processPhotoToDownload(photo)
}

// ProcessDelete handles property deletion
func ProcessDelete(changeDoc bson.M, id interface{}) error {
	golog.Info("Processing delete", "id", id)
	// For delete operations, we don't need to do anything since the document is already gone
	return nil
}

// getStringValue safely extracts string value from bson.M
func getStringValue(doc bson.M, key string) string {
	if val, ok := doc[key]; ok && val != nil {
		return fmt.Sprintf("%v", val)
	}
	return ""
}

// UpdateProcessStatus updates the process status
func UpdateProcessStatus(startTs time.Time) error {
	golog.Info("UpdateProcessStatus", "startTs", startTs)
	opts := goprocess.UpdateProcessStatusOptions{
		Status:   goprocess.RunningNormal,
		StartTs:  &startTs,
		ErrorMsg: nil,
		Stats:    nil,
	}
	return gProcessMonitor.UpdateProcessStatus(opts)
}

// OnTokenUpdate handles token updates
func OnTokenUpdate(tokenOpt gowatch.TokenUpdateOptions) error {
	tokenMu.Lock()
	defer tokenMu.Unlock()

	golog.Info("onTokenUpdate", "token", tokenOpt.Token, "gStat", gStat, "from:", lastTokenPrintTs)
	lastTokenPrintTs = time.Now()
	gStat = make(map[string]interface{})
	updateFields := gowatch.UpdateFields{
		Token:          tokenOpt.Token,
		TokenClusterTs: tokenOpt.TokenClusterTs,
		TokenChangeTs:  tokenOpt.TokenChangeTs,
		Status:         "running",
		ResumeMt:       tokenOpt.ResumeMt,
	}
	return gUpdateSysData(updateFields)
}

// OnChange handles property changes
func OnChange(changeDoc bson.M, coll *gomongo.MongoCollection) error {
	if gWatchedObject == nil {
		golog.Error("gWatchedObject is nil")
		return fmt.Errorf("gWatchedObject is nil")
	}
	return gowatch.ProcessChangedObject(gowatch.ProcessChangedObjectOptions{
		ChangedObj:  changeDoc,
		WatchedColl: coll,
		DeleteOneFn: func(id interface{}) error {
			return ProcessDelete(changeDoc, id)
		},
		InsertOneFn: func(prop bson.M) error {
			return ProcessInsert(changeDoc, prop)
		},
		ReplaceOneFn: func(prop bson.M) error {
			return ProcessReplace(changeDoc, prop)
		},
		UpdateOneFn: func(prop bson.M) error {
			return ProcessUpdate(changeDoc, prop)
		},
		WatchedStream: gWatchedObject,
	})
}

// WatchPhotoToDownload sets up the watch on photo_to_download collection
func WatchPhotoToDownload(ctx context.Context, cancel context.CancelFunc, token *bson.M, tokenClusterTs time.Time, resumeMt ...time.Time) error {
	// Use background context for watch operation
	var startTs time.Time
	if len(resumeMt) > 0 && !resumeMt[0].IsZero() {
		startTs = resumeMt[0]
	} else {
		startTs = time.Now()
		if !tokenClusterTs.IsZero() {
			golog.Debug("tokenClusterTs is not zero", "tokenClusterTs", tokenClusterTs)
			startTs = tokenClusterTs
		}
	}

	query := bson.M{"_mt": bson.M{"$gte": startTs}}
	opt := gowatch.WatchOptions{
		WatchedColl:           PhotoToDownloadCol,
		OnChange:              OnChange,
		OnTokenUpdate:         OnTokenUpdate,
		QueryWhenInvalidToken: query,
		SavedToken:            token,
		HighWaterMark:         20,
		OnError: func(err error) {
			golog.Error("watchPhotoToDownload error", "err", err.Error())
			GracefulExit(err)
			// Cancel context to unblock main goroutine waiting on <-ctx.Done()
			cancel()
		},
		UpdateProcessStatusFn: func() error {
			return UpdateProcessStatus(time.Now())
		},
		UpdateTokenTimerS: 120,
		Context:           ctx,
		Cancel:            cancel,
	}

	watchObj, err := gowatch.WatchTarget(opt)
	if err != nil {
		golog.Error("watchPhotoToDownload error", "err", err)
		GracefulExit(err)
	}
	gWatchedObject = watchObj
	return nil
}

// GetTokenAndWatch gets a new token and starts watching
func GetTokenAndWatch(ctx context.Context, cancel context.CancelFunc) error {
	token, tokenClusterTs, err := gowatch.GetToken(PhotoToDownloadCol)
	if err != nil {
		GracefulExit(err)
		return err
	}

	tokenOpt := gowatch.TokenUpdateOptions{
		Token:          token,
		TokenClusterTs: tokenClusterTs,
	}
	if err := OnTokenUpdate(tokenOpt); err != nil {
		GracefulExit(err)
		return err
	}
	golog.Debug("###token", "token", token)
	return WatchPhotoToDownload(ctx, cancel, token, tokenClusterTs)
}

// GetSignalChan returns a channel that informs about pressing Ctrl+C
func GetSignalChan() chan os.Signal {
	signalChannel := make(chan os.Signal, 1)
	signal.Notify(signalChannel,
		syscall.SIGHUP,
		syscall.SIGINT,
		syscall.SIGTERM,
		syscall.SIGQUIT)
	return signalChannel
}

// GracefulExit handles graceful shutdown
func GracefulExit(err error) {
	if err != nil {
		golog.Error("gracefulExit error", "err", err.Error())
	}

	if gWatchedObject != nil {
		if err := gWatchedObject.FinishAndUpdateSysdata(gUpdateSysData); err != nil {
			golog.Error("FinishAndUpdateSysdata error", "err", err.Error())
		}
	}

	errMsg := func() string {
		if err != nil {
			return err.Error()
		}
		return ""
	}()

	gProcessMonitor.UpdateStatusWhenAllFinish(errMsg, func() {
		golog.Info("UpdateStatusWhenAllFinish", "err", errMsg)
	})
}

// handleTokenAndWatch handles token retrieval and watching logic
func handleTokenAndWatch(watchCtx context.Context, watchCancel context.CancelFunc, sysData bson.M, startTs time.Time) error {
	token := sysData["token"]

	var tokenClusterTs primitive.DateTime
	if v, ok := sysData["tokenClusterTs"].(primitive.DateTime); ok {
		tokenClusterTs = v
	}

	var resumeMt time.Time
	if v, ok := sysData["resumeMt"].(primitive.DateTime); ok {
		resumeMt = v.Time()
	}

	// If no token or token is too old, start fresh
	if token == nil || (resumeMt.IsZero() && !tokenClusterTs.Time().After(startTs.Add(-HALF_DAY_IN_MS*time.Millisecond))) {
		golog.Info("Starting fresh watch")
		return GetTokenAndWatch(watchCtx, watchCancel)
	}

	// Parse and validate existing token
	var parsedToken bson.M
	var err error

	// Handle different token types for backward compatibility
	switch v := token.(type) {
	case string:
		// Token stored as JSON string (expected format)
		err = json.Unmarshal([]byte(v), &parsedToken)
	case bson.M:
		// Token stored as bson.M directly
		parsedToken = v
	case *bson.M:
		// Token stored as *bson.M directly
		if v != nil {
			parsedToken = *v
		} else {
			err = fmt.Errorf("token is nil pointer")
		}
	default:
		// Try to serialize and deserialize for other types
		tokenBytes, marshalErr := bson.MarshalExtJSON(v, false, false)
		if marshalErr != nil {
			err = fmt.Errorf("failed to marshal token of type %T: %v", v, marshalErr)
		} else {
			err = json.Unmarshal(tokenBytes, &parsedToken)
		}
	}

	if err != nil {
		golog.Info("Invalid token format, starting fresh", "token", token, "tokenType", fmt.Sprintf("%T", token), "error", err)
		return GetTokenAndWatch(watchCtx, watchCancel)
	}

	// Use existing token
	golog.Info("Continuing watch from existing token", "tokenClusterTs", tokenClusterTs)
	tokenOpt := gowatch.TokenUpdateOptions{
		Token:          &parsedToken,
		TokenClusterTs: tokenClusterTs.Time(),
	}
	if err := OnTokenUpdate(tokenOpt); err != nil {
		GracefulExit(err)
		return err
	}
	return WatchPhotoToDownload(watchCtx, watchCancel, &parsedToken, tokenClusterTs.Time(), resumeMt)
}

func main() {
	// Check for force parameter
	isForce := false
	dryRun = false
	for _, arg := range os.Args {
		switch arg {
		case "force":
			isForce = true
		case "dryrun":
			dryRun = true
		}
	}
	golog.Info("main", "force", isForce, "dryRun", dryRun)

	// Set up signal handling
	signalChannel := GetSignalChan()
	ctx, cancelCtx := context.WithCancel(context.Background())
	watchCtx, watchCancel := context.WithCancel(context.Background())

	// Handle signals
	go func() {
		sig := <-signalChannel
		sigName := "SIG" + strings.ToUpper(strings.TrimPrefix(sig.String(), "SIG"))
		errMsg := fmt.Sprintf("%s received %s", PROCESS_STATUS_ID, sigName)
		golog.Error(errMsg)
		GracefulExit(fmt.Errorf("%s", errMsg))
		signal.Stop(signalChannel)
		close(signalChannel)
		cancelCtx()
	}()

	// Check for running process
	isRunning, err := gProcessMonitor.CheckRunningProcess()
	if err != nil {
		// Only log error if it's not a "no documents" error
		if !strings.Contains(err.Error(), "no documents") {
			golog.Error("Failed to check running process",
				"error", err,
				"error_type", fmt.Sprintf("%T", err),
				"error_string", err.Error(),
				"process_id", PROCESS_STATUS_ID)
			GracefulExit(fmt.Errorf("failed to check running process: %v", err))
		}
		golog.Info("No existing process found, starting fresh", "process_id", PROCESS_STATUS_ID)
	}
	if isRunning && !isForce {
		golog.Warn("Process is already running", "process_id", PROCESS_STATUS_ID)
		GracefulExit(fmt.Errorf(goprocess.HasRunningProcess))
	}

	// Update process status
	startTs := time.Now()
	if err := UpdateProcessStatus(startTs); err != nil {
		golog.Error("Failed to update process status",
			"error", err,
			"error_type", fmt.Sprintf("%T", err),
			"error_string", err.Error(),
			"process_id", PROCESS_STATUS_ID)
		GracefulExit(fmt.Errorf("failed to update process status: %v", err))
	}

	// Process initial data on startup
	golog.Info("Processing initial data from photo_to_download table")
	if err := ProcessInitialData(); err != nil {
		golog.Error("Failed to process initial data", "error", err)
		GracefulExit(fmt.Errorf("failed to process initial data: %v", err))
	}

	// Get last resume token
	var sysData bson.M
	err = SysdataCol.FindOne(
		context.Background(),
		bson.M{"_id": PROCESS_STATUS_ID},
	).Decode(&sysData)

	if err != nil {
		if err.Error() == "mongo: no documents in result" {
			golog.Info("No existing sysdata found, starting fresh", "processId", PROCESS_STATUS_ID)
			if err := GetTokenAndWatch(watchCtx, watchCancel); err != nil {
				GracefulExit(err)
			}
		} else {
			golog.Error("Failed to get sysdata", "error", err.Error(), "processId", PROCESS_STATUS_ID)
			GracefulExit(err)
		}
	} else {
		if err := handleTokenAndWatch(watchCtx, watchCancel, sysData, startTs); err != nil {
			GracefulExit(err)
		}
	}

	// Wait for context to be cancelled
	<-ctx.Done()
	// Clean up ProcessMonitor
	if gProcessMonitor != nil {
		gProcessMonitor.StopMonitoring()
	}
}
